"use client"

import * as React from "react"
import { AdminLayout, DashboardStats, DashboardWidgets } from "@/components/admin"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
    TrendingUp,
    MessageCircle,
    Users,
    BarChart3,
    Sparkles,
    ArrowRight,
    Calendar,
    Globe,
    Plus
} from "lucide-react"

export default function AdminDashboard() {
    return (
        <AdminLayout>
            <div className="space-y-8">
                {/* Welcome Section */}
                 <Card className="glass-card border-white/10">
                 <div className="space-y-2 border-b border-white/10 p-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-foreground">
                                Welcome back, Admin! 👋
                            </h1>
                            <p className="text-muted-foreground mt-2">
                                Here's what's happening with your ChatCraft AI platform today.
                            </p>
                        </div>
                        <div className="hidden md:flex items-center space-x-3">
                            <Badge variant="secondary" className="bg-green-500/10 text-green-600 border-green-500/20">
                                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                                All systems operational
                            </Badge>
                            <Button className="gradient-pink-purple text-white">
                                <Plus className="w-4 h-4 mr-2" />
                                Create Widget
                            </Button>
                        </div>
                    </div>
                </div>
                </Card>
              

                {/* Stats Overview */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold text-foreground">Overview</h2>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4" />
                            <span>Last 30 days</span>
                        </div>
                    </div>
                    <DashboardStats />
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
                    {/* Left Column - Quick Actions & Activity */}
                    <div className="xl:col-span-2">
                        <DashboardWidgets />
                    </div>

                    {/* Right Column - Additional Info */}
                    <div className="space-y-6">
                        {/* Performance Summary */}
                        <Card className="glass-card border-white/10">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <TrendingUp className="w-5 h-5 text-green-500" />
                                    <span>Performance Summary</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">Response Rate</span>
                                        <span className="text-sm font-medium">98.5%</span>
                                    </div>
                                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div className="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full w-[98.5%]"></div>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">User Satisfaction</span>
                                        <span className="text-sm font-medium">94.2%</span>
                                    </div>
                                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div className="bg-gradient-to-r from-blue-500 to-cyan-600 h-2 rounded-full w-[94.2%]"></div>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">Conversion Rate</span>
                                        <span className="text-sm font-medium">24.8%</span>
                                    </div>
                                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div className="bg-gradient-to-r from-pink-500 to-purple-600 h-2 rounded-full w-[24.8%]"></div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Top Performing Widgets */}
                        <Card className="glass-card border-white/10">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Sparkles className="w-5 h-5 text-pink-500" />
                                    <span>Top Widgets</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {[
                                    { name: "Support Chat", conversations: "3,247", conversion: "28.5%" },
                                    { name: "Sales Assistant", conversations: "2,891", conversion: "31.2%" },
                                    { name: "FAQ Helper", conversations: "1,956", conversion: "18.7%" },
                                    { name: "Lead Capture", conversations: "1,423", conversion: "42.1%" }
                                ].map((widget, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-white/5 transition-colors">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center">
                                                <MessageCircle className="w-4 h-4 text-white" />
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-foreground">{widget.name}</p>
                                                <p className="text-xs text-muted-foreground">{widget.conversations} conversations</p>
                                            </div>
                                        </div>
                                        <Badge variant="secondary" className="text-xs">
                                            {widget.conversion}
                                        </Badge>
                                    </div>
                                ))}
                                <Button variant="ghost" size="sm" className="w-full mt-2">
                                    View all widgets
                                    <ArrowRight className="ml-1 h-3 w-3" />
                                </Button>
                            </CardContent>
                        </Card>

                        {/* System Status */}
                        <Card className="glass-card border-white/10">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Globe className="w-5 h-5 text-blue-500" />
                                    <span>System Status</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                {[
                                    { service: "API Gateway", status: "operational", uptime: "99.9%" },
                                    { service: "AI Processing", status: "operational", uptime: "99.8%" },
                                    { service: "Database", status: "operational", uptime: "100%" },
                                    { service: "CDN", status: "operational", uptime: "99.9%" }
                                ].map((service, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                                            <span className="text-sm text-foreground">{service.service}</span>
                                        </div>
                                        <span className="text-xs text-muted-foreground">{service.uptime}</span>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    )
} 