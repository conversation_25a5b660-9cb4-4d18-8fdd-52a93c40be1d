"use client"

import * as React from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AuthLayout } from "@/components/auth-layout"
import { 
  ArrowRight, 
  Mail, 
  ArrowLeft,
  CheckCircle,
  Clock
} from "lucide-react"

export default function ForgotPasswordPage() {
  const [email, setEmail] = React.useState("")
  const [isLoading, setIsLoading] = React.useState(false)
  const [isSubmitted, setIsSubmitted] = React.useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    console.log("Reset password for:", email)
    setIsLoading(false)
    setIsSubmitted(true)
  }

  const handleResend = () => {
    console.log("Resend email to:", email)
  }

  if (isSubmitted) {
    return (
      <AuthLayout
        title="Check Your Email"
        subtitle="We've sent password reset instructions to your email address."
      >
        <div className="space-y-6 text-center">
          {/* Success Icon */}
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-white" />
            </div>
          </div>

          {/* Header */}
          <div className="space-y-2">
            <h2 className="text-2xl font-bold">Email Sent!</h2>
            <p className="text-sm text-muted-foreground">
              We've sent a password reset link to
            </p>
            <p className="text-sm font-medium text-pink-500">{email}</p>
          </div>

          {/* Instructions */}
          <div className="glass p-4 rounded-lg space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                1
              </div>
              <span className="text-sm">Check your email inbox</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                2
              </div>
              <span className="text-sm">Click the reset password link</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                3
              </div>
              <span className="text-sm">Create your new password</span>
            </div>
          </div>

          {/* Timer */}
          <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>Link expires in 15 minutes</span>
          </div>

          {/* Actions */}
          <div className="space-y-3">
            <Button
              onClick={handleResend}
              variant="outline"
              className="w-full glass border-white/20 hover:bg-white/10"
            >
              Resend Email
            </Button>
            
            <Button
              asChild
              className="w-full gradient-pink-purple text-white hover:opacity-90 transition-opacity"
            >
              <Link href="/auth/signin">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Sign In
              </Link>
            </Button>
          </div>

          {/* Help */}
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              Didn't receive the email? Check your spam folder or{" "}
              <Link
                href="/contact"
                className="text-pink-500 hover:text-pink-600 transition-colors"
              >
                contact support
              </Link>
            </p>
          </div>
        </div>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout
      title="Reset Password"
      subtitle="Enter your email address and we'll send you a link to reset your password."
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-bold">Forgot Password?</h2>
          <p className="text-sm text-muted-foreground">
            No worries! Enter your email and we'll send you reset instructions.
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="pl-10 glass-card border-white/20 focus:border-pink-500/50 focus:ring-pink-500/20"
                required
              />
            </div>
          </div>

          <Button
            type="submit"
            className="w-full gradient-pink-purple text-white hover:opacity-90 transition-opacity"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                <span>Sending...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <span>Send Reset Link</span>
                <ArrowRight className="h-4 w-4" />
              </div>
            )}
          </Button>
        </form>

        {/* Back to Sign In */}
        <div className="text-center">
          <Button variant="ghost" asChild className="text-sm">
            <Link href="/auth/signin" className="flex items-center space-x-2">
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Sign In</span>
            </Link>
          </Button>
        </div>

        {/* Help */}
        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            Need help?{" "}
            <Link
              href="/contact"
              className="text-pink-500 hover:text-pink-600 transition-colors"
            >
              Contact our support team
            </Link>
          </p>
        </div>
      </div>
    </AuthLayout>
  )
}
