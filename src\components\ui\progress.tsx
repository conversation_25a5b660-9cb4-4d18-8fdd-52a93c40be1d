import * as React from "react"

import { cn } from "@/lib/utils"

function Progress({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="progress"
      className={cn(
        "relative h-4 w-full overflow-hidden rounded-full bg-primary/20",
        className
      )}
      {...props}
    />
  )
}

function ProgressIndicator({
  className,    
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="progress-indicator"
      className={cn(
        "h-full bg-primary transition-all duration-300",
        className
      )}        
      {...props}
    />
  )
}

export { Progress, ProgressIndicator }