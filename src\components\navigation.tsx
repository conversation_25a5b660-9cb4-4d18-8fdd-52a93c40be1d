"use client"

import * as React from "react"
import Link from "next/link"
import { useTheme } from "next-themes"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Menu, 
  X, 
  Sun, 
  Moon, 
  MessageCircle, 
  Sparkles,
  ChevronDown 
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function Navigation() {
  const [isOpen, setIsOpen] = React.useState(false)
  const { theme, setTheme } = useTheme()

  const toggleMenu = () => setIsOpen(!isOpen)

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 glass-card border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="relative">
              <MessageCircle className="h-8 w-8 text-pink-500 group-hover:scale-110 transition-transform duration-300" />
              <Sparkles className="h-4 w-4 text-purple-500 absolute -top-1 -right-1 animate-pulse" />
            </div>
            <span className="text-xl font-bold gradient-text">ChatCraft AI</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-1">
                  <span>Features</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="glass-card">
                <DropdownMenuItem>
                  <Link href="#features" className="w-full">AI Chat Builder</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="#features" className="w-full">Custom Styling</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="#features" className="w-full">Analytics</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link href="#features" className="w-full">Integrations</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <Link href="#pricing" className="text-foreground hover:text-pink-500 transition-colors">
              Pricing
            </Link>
            <Link href="#testimonials" className="text-foreground hover:text-pink-500 transition-colors">
              Testimonials
            </Link>
            <Link href="#faq" className="text-foreground hover:text-pink-500 transition-colors">
              FAQ
            </Link>
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="hover:bg-white/10"
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>
            
            <Button variant="ghost">
              Sign In
            </Button>
            
            <Button className="gradient-pink-purple text-white hover:opacity-90 transition-opacity">
              Start Free Trial
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="hover:bg-white/10"
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMenu}
              className="hover:bg-white/10"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 glass border-t border-white/10">
              <Link
                href="#features"
                className="block px-3 py-2 text-base font-medium text-foreground hover:text-pink-500 transition-colors"
                onClick={toggleMenu}
              >
                Features
              </Link>
              <Link
                href="#pricing"
                className="block px-3 py-2 text-base font-medium text-foreground hover:text-pink-500 transition-colors"
                onClick={toggleMenu}
              >
                Pricing
              </Link>
              <Link
                href="#testimonials"
                className="block px-3 py-2 text-base font-medium text-foreground hover:text-pink-500 transition-colors"
                onClick={toggleMenu}
              >
                Testimonials
              </Link>
              <Link
                href="#faq"
                className="block px-3 py-2 text-base font-medium text-foreground hover:text-pink-500 transition-colors"
                onClick={toggleMenu}
              >
                FAQ
              </Link>
              <div className="pt-4 border-t border-white/10 space-y-2">
                <Button variant="ghost" className="w-full justify-start">
                  Sign In
                </Button>
                <Button className="w-full gradient-pink-purple text-white">
                  Start Free Trial
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
