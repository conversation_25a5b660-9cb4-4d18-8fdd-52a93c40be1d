"use client"

import * as React from "react"
import { AdminLayout } from "@/components/admin"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
    Plus,
    Search,
    Filter,
    MoreHorizontal,
    MessageCircle,
    Eye,
    Edit,
    Trash2,
    Copy,
    Settings,
    BarChart3
} from "lucide-react"

const widgets = [
    {
        id: "1",
        name: "Support Chat Widget",
        description: "24/7 customer support chat for the main website",
        status: "active",
        conversations: 3247,
        conversion: "28.5%",
        lastUpdated: "2 hours ago",
        domain: "example.com"
    },
    {
        id: "2",
        name: "Sales Assistant",
        description: "Lead qualification and sales support for product pages",
        status: "active",
        conversations: 2891,
        conversion: "31.2%",
        lastUpdated: "1 day ago",
        domain: "shop.example.com"
    },
    {
        id: "3",
        name: "FAQ Helper",
        description: "Automated FAQ responses for common questions",
        status: "paused",
        conversations: 1956,
        conversion: "18.7%",
        lastUpdated: "3 days ago",
        domain: "help.example.com"
    },
    {
        id: "4",
        name: "Lead Capture Bot",
        description: "Captures visitor information and qualifies leads",
        status: "active",
        conversations: 1423,
        conversion: "42.1%",
        lastUpdated: "5 hours ago",
        domain: "landing.example.com"
    }
]

export default function WidgetsPage() {
    const [searchQuery, setSearchQuery] = React.useState("")

    const filteredWidgets = widgets.filter(widget =>
        widget.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        widget.description.toLowerCase().includes(searchQuery.toLowerCase())
    )

    return (
        <AdminLayout>
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground">Chat Widgets</h1>
                        <p className="text-muted-foreground mt-2">
                            Manage and monitor your AI-powered chat widgets
                        </p>
                    </div>
                    <Button className="gradient-pink-purple text-white">
                        <Plus className="w-4 h-4 mr-2" />
                        Create Widget
                    </Button>
                </div>

                {/* Filters and Search */}
                <div className="flex items-center justify-between space-x-4">
                    <div className="flex items-center space-x-4 flex-1">
                        <div className="relative max-w-md">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="Search widgets..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-10 glass-card border-white/20"
                            />
                        </div>
                        <Button variant="outline" className="glass-card border-white/20">
                            <Filter className="w-4 h-4 mr-2" />
                            Filter
                        </Button>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <span>{filteredWidgets.length} widgets</span>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card className="glass-card border-white/10">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Total Widgets</p>
                                    <p className="text-2xl font-bold text-foreground">4</p>
                                </div>
                                <div className="p-3 bg-pink-500/10 border border-pink-500/20 rounded-lg">
                                    <MessageCircle className="h-5 w-5 text-pink-500" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="glass-card border-white/10">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Active Widgets</p>
                                    <p className="text-2xl font-bold text-foreground">3</p>
                                </div>
                                <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                                    <Eye className="h-5 w-5 text-green-500" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="glass-card border-white/10">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Total Conversations</p>
                                    <p className="text-2xl font-bold text-foreground">9.5k</p>
                                </div>
                                <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                                    <BarChart3 className="h-5 w-5 text-blue-500" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="glass-card border-white/10">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Avg Conversion</p>
                                    <p className="text-2xl font-bold text-foreground">30.1%</p>
                                </div>
                                <div className="p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                                    <Settings className="h-5 w-5 text-purple-500" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Widgets List */}
                <Card className="glass-card border-white/10">
                    <CardHeader>
                        <CardTitle>All Widgets</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="space-y-0">
                            {filteredWidgets.map((widget, index) => (
                                <div
                                    key={widget.id}
                                    className={`p-6 hover:bg-white/5 transition-colors ${index !== filteredWidgets.length - 1 ? "border-b border-white/10" : ""
                                        }`}
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-4">
                                            <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-purple-600 rounded-xl flex items-center justify-center">
                                                <MessageCircle className="w-6 h-6 text-white" />
                                            </div>
                                            <div className="space-y-1">
                                                <div className="flex items-center space-x-3">
                                                    <h3 className="font-semibold text-foreground">{widget.name}</h3>
                                                    <Badge
                                                        variant="secondary"
                                                        className={
                                                            widget.status === "active"
                                                                ? "bg-green-500/10 text-green-600 border-green-500/20"
                                                                : "bg-yellow-500/10 text-yellow-600 border-yellow-500/20"
                                                        }
                                                    >
                                                        {widget.status}
                                                    </Badge>
                                                </div>
                                                <p className="text-sm text-muted-foreground">{widget.description}</p>
                                                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                                    <span>{widget.domain}</span>
                                                    <span>•</span>
                                                    <span>Updated {widget.lastUpdated}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-center space-x-6">
                                            <div className="text-right space-y-1">
                                                <p className="text-sm font-medium text-foreground">
                                                    {widget.conversations.toLocaleString()} conversations
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    {widget.conversion} conversion rate
                                                </p>
                                            </div>

                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" size="icon">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end" className="glass-card">
                                                    <DropdownMenuItem>
                                                        <Eye className="mr-2 h-4 w-4" />
                                                        View Details
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem>
                                                        <Edit className="mr-2 h-4 w-4" />
                                                        Edit Widget
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem>
                                                        <Copy className="mr-2 h-4 w-4" />
                                                        Duplicate
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem>
                                                        <BarChart3 className="mr-2 h-4 w-4" />
                                                        Analytics
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem className="text-red-600 dark:text-red-400">
                                                        <Trash2 className="mr-2 h-4 w-4" />
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    )
} 