"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import {
    LayoutDashboard,
    MessageCircle,
    Sparkles,
    Bot,
    BarChart3,
    Settings,
    Users,
    Palette,
    Plug,
    HelpCircle,
    X
} from "lucide-react"

interface AdminSidebarProps {
    isOpen: boolean
    onClose: () => void
}

const navigationItems = [
    {
        title: "Dashboard",
        href: "/admin",
        icon: LayoutDashboard,
        description: "Overview and analytics"
    },
    {
        title: "Chat Widgets",
        href: "/admin/widgets",
        icon: MessageCircle,
        description: "Manage your chat widgets"
    },
    {
        title: "AI Models",
        href: "/admin/ai-models",
        icon: Bo<PERSON>,
        description: "Configure AI providers"
    },
    {
        title: "Analytics",
        href: "/admin/analytics",
        icon: Bar<PERSON>hart3,
        description: "Performance insights"
    },
    {
        title: "Users",
        href: "/admin/users",
        icon: Users,
        description: "User management"
    },
    {
        title: "Customization",
        href: "/admin/customization",
        icon: Pa<PERSON>,
        description: "Styling and branding"
    },
    {
        title: "Integrations",
        href: "/admin/integrations",
        icon: Plug,
        description: "Third-party connections"
    },
    {
        title: "Settings",
        href: "/admin/settings",
        icon: Settings,
        description: "Account and preferences"
    },
    {
        title: "Help & Support",
        href: "/admin/help",
        icon: HelpCircle,
        description: "Documentation and support"
    }
]

export function AdminSidebar({ isOpen, onClose }: AdminSidebarProps) {
    const pathname = usePathname()

    return (
        <>
            {/* Desktop Sidebar */}
            <aside className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 z-50">
                <div className="flex flex-col flex-1 glass-card border-r border-white/10 backdrop-blur-xl">
                    {/* Logo */}
                    <div className="flex items-center justify-center px-6 py-8 border-b border-white/10">
                        <Link href="/admin" className="flex items-center space-x-3 group">
                            <div className="relative">
                                <MessageCircle className="h-8 w-8 text-pink-500 group-hover:scale-110 transition-transform duration-300" />
                                <Sparkles className="h-4 w-4 text-purple-500 absolute -top-1 -right-1 animate-pulse" />
                            </div>
                            <div className="flex flex-col">
                                <span className="text-lg font-bold gradient-text">ChatCraft AI</span>
                                <span className="text-xs text-muted-foreground">Admin Panel</span>
                            </div>
                        </Link>
                    </div>

                    {/* Navigation */}
                    <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                        {navigationItems.map((item) => {
                            const isActive = pathname === item.href || (item.href !== "/admin" && pathname.startsWith(item.href))
                            const Icon = item.icon

                            return (
                                <Link
                                    key={item.href}
                                    href={item.href}
                                    className={cn(
                                        "flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group",
                                        isActive
                                            ? "bg-gradient-to-r from-pink-500/20 to-purple-600/20 text-pink-600 dark:text-pink-400 border border-pink-500/30"
                                            : "text-muted-foreground hover:text-foreground hover:bg-white/10 dark:hover:bg-white/5"
                                    )}
                                >
                                    <Icon className={cn(
                                        "mr-3 h-5 w-5 transition-colors",
                                        isActive ? "text-pink-500" : "text-muted-foreground group-hover:text-foreground"
                                    )} />
                                    <div className="flex flex-col">
                                        <span>{item.title}</span>
                                        <span className="text-xs text-muted-foreground/70">{item.description}</span>
                                    </div>
                                </Link>
                            )
                        })}
                    </nav>

                    {/* Footer */}
                    <div className="px-4 py-4 border-t border-white/10">
                        <div className="glass p-4 rounded-xl text-center">
                            <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <Sparkles className="w-4 h-4 text-white" />
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Need help? Check our documentation
                            </p>
                        </div>
                    </div>
                </div>
            </aside>

            {/* Mobile Sidebar */}
            <aside className={cn(
                "fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:hidden",
                isOpen ? "translate-x-0" : "-translate-x-full"
            )}>
                <div className="flex flex-col flex-1 h-full glass-card border-r border-white/10 backdrop-blur-xl">
                    {/* Mobile Header */}
                    <div className="flex items-center justify-between px-6 py-4 border-b border-white/10">
                        <Link href="/admin" className="flex items-center space-x-3 group" onClick={onClose}>
                            <div className="relative">
                                <MessageCircle className="h-6 w-6 text-pink-500" />
                                <Sparkles className="h-3 w-3 text-purple-500 absolute -top-1 -right-1 animate-pulse" />
                            </div>
                            <div className="flex flex-col">
                                <span className="text-sm font-bold gradient-text">ChatCraft AI</span>
                                <span className="text-xs text-muted-foreground">Admin</span>
                            </div>
                        </Link>
                        <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
                            <X className="h-4 w-4" />
                        </Button>
                    </div>

                    {/* Mobile Navigation */}
                    <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
                        {navigationItems.map((item) => {
                            const isActive = pathname === item.href || (item.href !== "/admin" && pathname.startsWith(item.href))
                            const Icon = item.icon

                            return (
                                <Link
                                    key={item.href}
                                    href={item.href}
                                    onClick={onClose}
                                    className={cn(
                                        "flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",
                                        isActive
                                            ? "bg-gradient-to-r from-pink-500/20 to-purple-600/20 text-pink-600 dark:text-pink-400"
                                            : "text-muted-foreground hover:text-foreground hover:bg-white/10"
                                    )}
                                >
                                    <Icon className={cn(
                                        "mr-3 h-4 w-4",
                                        isActive ? "text-pink-500" : "text-muted-foreground"
                                    )} />
                                    {item.title}
                                </Link>
                            )
                        })}
                    </nav>
                </div>
            </aside>
        </>
    )
} 