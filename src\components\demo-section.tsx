"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  MessageCircle,
  Sparkles,
  Zap,
  Bot,
  Send,
  Settings,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>3,
  Play,
  ArrowRight
} from "lucide-react"

export function DemoSection() {
  const [chatMessages, setChatMessages] = React.useState([
    { id: 1, type: 'bot', message: 'Hi! How can I help you today?', time: '2:30 PM' },
    { id: 2, type: 'user', message: 'I need help with pricing', time: '2:31 PM' },
    { id: 3, type: 'bot', message: 'I\'d be happy to help! We have flexible plans starting at $29/month. Would you like to see our pricing options?', time: '2:31 PM' }
  ])
  const [isTyping, setIsTyping] = React.useState(false)
  const [activeTab, setActiveTab] = React.useState('preview')
  const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 })
  const demoRef = React.useRef<HTMLDivElement>(null)

  const simulateChat = () => {
    setIsTyping(true)
    setTimeout(() => {
      setChatMessages(prev => [...prev, {
        id: prev.length + 1,
        type: 'user',
        message: 'That sounds great! Can I try it for free?',
        time: '2:32 PM'
      }])
      setIsTyping(false)
      
      setTimeout(() => {
        setIsTyping(true)
        setTimeout(() => {
          setChatMessages(prev => [...prev, {
            id: prev.length + 1,
            type: 'bot',
            message: 'Absolutely! We offer a 14-day free trial with full access to all features. No credit card required!',
            time: '2:32 PM'
          }])
          setIsTyping(false)
        }, 1500)
      }, 1000)
    }, 1000)
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!demoRef.current) return
    
    const rect = demoRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    const centerX = rect.width / 2
    const centerY = rect.height / 2
    
    const rotateX = (y - centerY) / centerY * -10
    const rotateY = (x - centerX) / centerX * 10
    
    setMousePosition({ x: rotateX, y: rotateY })
  }

  const handleMouseLeave = () => {
    setMousePosition({ x: 0, y: 0 })
  }

  return (
    <section className="py-24 relative overflow-hidden bg-gradient-to-b from-background to-muted/20">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid opacity-30" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 float-3d">
        <div className="glass p-4 rounded-full">
          <MessageCircle className="h-6 w-6 text-pink-500" />
        </div>
      </div>
      
      <div className="absolute top-40 right-20 float-3d" style={{ animationDelay: "2s" }}>
        <div className="glass p-4 rounded-full">
          <Sparkles className="h-6 w-6 text-purple-500" />
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="glass-card px-4 py-2 mx-auto">
            <Play className="h-4 w-4 mr-2 text-pink-500" />
            Interactive Demo
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight">
            See ChatCraft AI in{" "}
            <span className="gradient-text">Action</span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Experience the power of our AI chat widget builder with this interactive demo. 
            Try different features and see how easy it is to create professional chat widgets.
          </p>
        </div>

        {/* Interactive 3D Demo */}
        <div className="relative max-w-5xl mx-auto">
          <div className="relative perspective-1000">
            {/* Demo Container */}
            <div 
              ref={demoRef}
              onMouseMove={handleMouseMove}
              onMouseLeave={handleMouseLeave}
              className="glass-card p-8 rounded-2xl shadow-2xl transition-transform duration-300 ease-out"
              style={{
                transform: `perspective(1000px) rotateX(${mousePosition.x}deg) rotateY(${mousePosition.y}deg) scale(1.02)`
              }}
            >
              {/* Demo Tabs */}
              <div className="flex flex-wrap justify-center gap-2 mb-8">
                <button
                  onClick={() => setActiveTab('preview')}
                  className={`px-6 py-3 rounded-lg text-sm font-medium transition-all ${
                    activeTab === 'preview' 
                      ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg' 
                      : 'glass hover:bg-white/10'
                  }`}
                >
                  <MessageCircle className="h-4 w-4 inline mr-2" />
                  Live Chat Preview
                </button>
                <button
                  onClick={() => setActiveTab('builder')}
                  className={`px-6 py-3 rounded-lg text-sm font-medium transition-all ${
                    activeTab === 'builder' 
                      ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg' 
                      : 'glass hover:bg-white/10'
                  }`}
                >
                  <Settings className="h-4 w-4 inline mr-2" />
                  Visual Builder
                </button>
                <button
                  onClick={() => setActiveTab('analytics')}
                  className={`px-6 py-3 rounded-lg text-sm font-medium transition-all ${
                    activeTab === 'analytics' 
                      ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg' 
                      : 'glass hover:bg-white/10'
                  }`}
                >
                  <BarChart3 className="h-4 w-4 inline mr-2" />
                  Analytics Dashboard
                </button>
              </div>

              {/* Demo Content */}
              <div className="min-h-[500px]">
                {activeTab === 'preview' && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    {/* Chat Widget */}
                    <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden max-w-md mx-auto">
                      {/* Chat Header */}
                      <div className="bg-gradient-to-r from-pink-500 to-purple-600 p-4 text-white">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                            <Bot className="h-6 w-6" />
                          </div>
                          <div>
                            <h4 className="font-semibold">AI Assistant</h4>
                            <p className="text-xs opacity-90">Online • Typically replies instantly</p>
                          </div>
                        </div>
                      </div>

                      {/* Chat Messages */}
                      <div className="p-4 space-y-3 h-80 overflow-y-auto">
                        {chatMessages.map((msg) => (
                          <div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                            <div className={`max-w-xs px-4 py-2 rounded-2xl ${
                              msg.type === 'user' 
                                ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white' 
                                : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
                            }`}>
                              <p className="text-sm">{msg.message}</p>
                              <p className={`text-xs mt-1 ${
                                msg.type === 'user' ? 'text-white/70' : 'text-gray-500'
                              }`}>
                                {msg.time}
                              </p>
                            </div>
                          </div>
                        ))}
                        
                        {isTyping && (
                          <div className="flex justify-start">
                            <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 rounded-2xl">
                              <div className="flex space-x-1">
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Chat Input */}
                      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                        <div className="flex space-x-2">
                          <input
                            type="text"
                            placeholder="Type your message..."
                            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-pink-500"
                          />
                          <button
                            onClick={simulateChat}
                            className="p-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-lg hover:opacity-90 transition-opacity"
                          >
                            <Send className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Features List */}
                    <div className="space-y-6">
                      <h3 className="text-2xl font-bold">Try the Live Demo</h3>
                      <div className="space-y-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <MessageCircle className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <h4 className="font-semibold">Real-time Conversations</h4>
                            <p className="text-sm text-muted-foreground">Click the send button to see AI responses in action</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <Sparkles className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <h4 className="font-semibold">Smart Responses</h4>
                            <p className="text-sm text-muted-foreground">AI understands context and provides helpful answers</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <Zap className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <h4 className="font-semibold">Instant Setup</h4>
                            <p className="text-sm text-muted-foreground">Get this running on your site in under 5 minutes</p>
                          </div>
                        </div>
                      </div>
                      
                      <Button className="gradient-pink-purple text-white group">
                        Start Building Your Widget
                        <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </div>
                  </div>
                )}

                {activeTab === 'builder' && (
                  <div className="space-y-8">
                    <div className="text-center">
                      <h3 className="text-2xl font-bold mb-4">Visual Builder Interface</h3>
                      <p className="text-muted-foreground">Drag, drop, and customize your chat widget without any coding</p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      <div className="glass p-6 rounded-lg text-center hover-lift">
                        <Palette className="h-12 w-12 text-pink-500 mx-auto mb-4" />
                        <h4 className="font-semibold mb-2">Custom Styling</h4>
                        <p className="text-sm text-muted-foreground">Match your brand colors, fonts, and design perfectly</p>
                      </div>
                      <div className="glass p-6 rounded-lg text-center hover-lift">
                        <Bot className="h-12 w-12 text-purple-500 mx-auto mb-4" />
                        <h4 className="font-semibold mb-2">AI Training</h4>
                        <p className="text-sm text-muted-foreground">Train the AI with your business data and FAQs</p>
                      </div>
                      <div className="glass p-6 rounded-lg text-center hover-lift">
                        <Settings className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                        <h4 className="font-semibold mb-2">Flow Builder</h4>
                        <p className="text-sm text-muted-foreground">Create conversation flows with visual editor</p>
                      </div>
                    </div>
                    
                    <div className="glass p-8 rounded-lg text-center">
                      <div className="w-16 h-16 mx-auto bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
                        <Settings className="h-8 w-8 text-white" />
                      </div>
                      <h4 className="text-xl font-semibold mb-2">Drag & Drop Builder</h4>
                      <p className="text-muted-foreground mb-6">Build your chat widget visually with our intuitive interface. No coding required!</p>
                      <Button variant="outline" className="glass border-white/20">
                        Try the Builder
                      </Button>
                    </div>
                  </div>
                )}

                {activeTab === 'analytics' && (
                  <div className="space-y-8">
                    <div className="text-center">
                      <h3 className="text-2xl font-bold mb-4">Analytics Dashboard</h3>
                      <p className="text-muted-foreground">Track performance and optimize your chat widget with detailed insights</p>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="glass p-4 rounded-lg text-center">
                        <div className="text-3xl font-bold gradient-text">2.4k</div>
                        <div className="text-sm text-muted-foreground">Conversations</div>
                      </div>
                      <div className="glass p-4 rounded-lg text-center">
                        <div className="text-3xl font-bold gradient-text">94%</div>
                        <div className="text-sm text-muted-foreground">Satisfaction</div>
                      </div>
                      <div className="glass p-4 rounded-lg text-center">
                        <div className="text-3xl font-bold gradient-text">1.2s</div>
                        <div className="text-sm text-muted-foreground">Avg Response</div>
                      </div>
                      <div className="glass p-4 rounded-lg text-center">
                        <div className="text-3xl font-bold gradient-text">67%</div>
                        <div className="text-sm text-muted-foreground">Conversion</div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="glass p-6 rounded-lg">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-semibold">Performance Metrics</h4>
                          <BarChart3 className="h-5 w-5 text-pink-500" />
                        </div>
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between text-sm mb-1">
                              <span>Lead Conversion</span>
                              <span className="text-green-500">+23%</span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div className="bg-gradient-to-r from-pink-500 to-purple-600 h-2 rounded-full w-3/4"></div>
                            </div>
                          </div>
                          <div>
                            <div className="flex justify-between text-sm mb-1">
                              <span>User Engagement</span>
                              <span className="text-green-500">+45%</span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div className="bg-gradient-to-r from-blue-500 to-cyan-600 h-2 rounded-full w-4/5"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="glass p-6 rounded-lg">
                        <h4 className="font-semibold mb-4">Real-time Insights</h4>
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Active Conversations</span>
                            <span className="font-semibold">12</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Response Time</span>
                            <span className="font-semibold text-green-500">1.2s</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Satisfaction Score</span>
                            <span className="font-semibold text-yellow-500">4.8/5</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Floating Elements around Demo */}
            <div className="absolute -top-4 -right-4 pulse-3d">
              <div className="glass-card p-3 rounded-full shadow-lg">
                <Sparkles className="h-5 w-5 text-pink-500" />
              </div>
            </div>
            
            <div className="absolute -bottom-4 -left-4 float-3d">
              <div className="glass-card p-3 rounded-full shadow-lg">
                <Zap className="h-5 w-5 text-purple-500" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
