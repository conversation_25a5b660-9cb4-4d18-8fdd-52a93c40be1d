# ChatCraft AI - Stunning SaaS Landing Page

A comprehensive, modern SaaS landing page for an AI-powered chat widget builder platform. Built with Next.js 15, TypeScript, Tailwind CSS, and Shadcn UI components.

![ChatCraft AI Landing Page](https://via.placeholder.com/1200x600/F472B6/FFFFFF?text=ChatCraft+AI+Landing+Page)

## 🎨 Design Features

### **Modern Design System**
- **Glassmorphism styling** with backdrop blur effects
- **Pink-to-purple gradient** accent colors (`#F472B6` to `#6B7280`)
- **Clean typography** using Inter font family
- **Light/dark mode** support with smooth transitions
- **Micro-interactions** on hover and click states
- **Responsive design** optimized for all screen sizes

### **Visual Elements**
- Custom gradient backgrounds and floating elements
- Smooth animations and transitions
- Hover effects on cards and buttons
- Mobile-responsive navigation with glassmorphism
- Professional color palette with CSS custom properties

## 🚀 Landing Page Sections

### **1. Navigation Header**
- Fixed glassmorphism navigation bar
- Mobile-responsive hamburger menu
- Theme toggle (light/dark mode)
- Smooth dropdown menus
- Call-to-action buttons

### **2. Hero Section**
- Compelling value proposition
- Email capture form with validation
- Animated floating elements
- Interactive demo preview
- Trust indicators and social proof

### **3. Features Showcase**
- **4 Core Features** with detailed descriptions:
  - AI-Powered Conversations (GPT-4 integration)
  - Custom Styling & Branding (Visual builder)
  - Advanced Analytics (Performance insights)
  - Seamless Integrations (50+ platforms)
- Additional feature grid with 6 supplementary features
- Hover animations and glassmorphism cards

### **4. Customer Testimonials**
- 6 authentic customer stories with metrics
- Star ratings and company information
- Performance statistics dashboard
- Social proof indicators

### **5. FAQ Section**
- 8 comprehensive frequently asked questions
- Accordion-style expandable answers
- Support contact information
- Professional help center styling

### **6. Call-to-Action Section**
- Email signup with success states
- Trust indicators and benefits
- Secondary demo CTA
- Company logo showcase

### **7. Professional Footer**
- Newsletter subscription
- Comprehensive link sections (Product, Company, Resources, Legal)
- Social media links
- Copyright and branding

## 🛠️ Technical Stack

### **Core Technologies**
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS 4.1** - Utility-first styling
- **Shadcn UI** - Modern component library

### **UI Components & Libraries**
- **Radix UI** - Headless component primitives
- **Lucide React** - Beautiful icon library
- **Framer Motion** - Animation library
- **next-themes** - Theme management
- **class-variance-authority** - Component variants
- **tailwind-merge** - Intelligent class merging

### **Form Handling & Validation**
- **React Hook Form** - Performant forms
- **Zod** - Schema validation
- **@hookform/resolvers** - Form validation resolvers

## 📁 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and custom utilities
│   ├── layout.tsx           # Root layout with theme provider
│   └── page.tsx             # Main landing page
├── components/
│   ├── ui/                  # Shadcn UI components
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── input.tsx
│   │   ├── badge.tsx
│   │   └── ...
│   ├── navigation.tsx       # Header navigation
│   ├── hero-section.tsx     # Hero section
│   ├── features-section.tsx # Features showcase
│   ├── testimonials-section.tsx # Customer testimonials
│   ├── faq-section.tsx      # FAQ accordion
│   ├── cta-section.tsx      # Call-to-action
│   ├── footer.tsx           # Footer
│   └── theme-provider.tsx   # Theme context provider
└── lib/
    └── utils.ts             # Utility functions
```

## 🎯 Key Features Implemented

### **Design System**
- ✅ Glassmorphism effects with backdrop blur
- ✅ Custom gradient utilities and animations
- ✅ Dark/light mode with smooth transitions
- ✅ Responsive design patterns
- ✅ Hover effects and micro-interactions

### **Performance Optimizations**
- ✅ Next.js 15 App Router for optimal performance
- ✅ TypeScript for type safety
- ✅ Optimized fonts with next/font
- ✅ Efficient component architecture
- ✅ CSS custom properties for theming

### **User Experience**
- ✅ Smooth scrolling navigation
- ✅ Form validation and success states
- ✅ Mobile-first responsive design
- ✅ Accessibility considerations
- ✅ Loading states and animations

## 🚀 Getting Started

### **Prerequisites**
- Node.js 18+
- npm, yarn, or pnpm

### **Installation**

1. **Clone the repository**
```bash
git clone <repository-url>
cd aug-test
```

2. **Install dependencies**
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. **Run the development server**
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000) to see the landing page.

### **Build for Production**
```bash
npm run build
npm start
```

## 🎨 Customization

### **Colors & Branding**
The design system uses CSS custom properties for easy customization:

```css
/* In globals.css */
--color-gradient-pink: #F472B6;
--color-gradient-purple: #6B7280;
```

### **Content Updates**
- Update company information in each component
- Modify testimonials in `testimonials-section.tsx`
- Customize FAQ content in `faq-section.tsx`
- Update feature descriptions in `features-section.tsx`

### **Styling Modifications**
- Glassmorphism effects: `.glass` and `.glass-card` classes
- Gradients: `.gradient-pink-purple` and `.gradient-text`
- Animations: `.animate-float`, `.animate-fade-in`, `.animate-slide-up`

## 📱 Responsive Design

The landing page is fully responsive with breakpoints:
- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

## 🌙 Theme Support

Built-in dark/light mode support with:
- System preference detection
- Manual theme toggle
- Smooth transitions between themes
- Persistent theme selection

## 🔧 Development

### **Adding New Components**
1. Create component in `src/components/`
2. Export from main page or layout
3. Follow existing patterns for styling and props

### **Extending the Design System**
1. Add new utilities to `globals.css`
2. Use existing color variables
3. Follow glassmorphism and gradient patterns

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

---

**Built with ❤️ using Next.js, TypeScript, and Tailwind CSS**
