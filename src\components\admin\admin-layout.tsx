"use client"

import * as React from "react"
import { useTheme } from "next-themes"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminTopbar } from "@/components/admin/admin-topbar"
import { cn } from "@/lib/utils"

interface AdminLayoutProps {
    children: React.ReactNode
    className?: string
}

export function AdminLayout({ children, className }: AdminLayoutProps) {
    const [sidebarOpen, setSidebarOpen] = React.useState(false)

    return (
        <div className="min-h-screen bg-gradient-to-br from-pink-50/30 via-purple-50/20 to-blue-50/30 dark:from-pink-950/10 dark:via-purple-950/10 dark:to-blue-950/10">
            {/* Background Grid */}
            <div className="fixed inset-0 bg-grid opacity-20 pointer-events-none" />

            <div className="relative flex h-screen">
                {/* Sidebar */}
                <AdminSidebar
                    isOpen={sidebarOpen}
                    onClose={() => setSidebarOpen(false)}
                />

                {/* Main Content Area */}
                <div className="flex-1 flex flex-col min-w-0 lg:ml-64">
                    {/* Topbar */}
                    <AdminTopbar
                        onMenuClick={() => setSidebarOpen(true)}
                        sidebarOpen={sidebarOpen}
                    />

                    {/* Main Content */}
                    <main className={cn(
                        "flex-1 overflow-auto p-6 space-y-6 w-full",
                        className
                    )}>
                        {children}
                    </main>
                </div>
            </div>

            {/* Mobile Sidebar Overlay */}
            {sidebarOpen && (
                <div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
                    onClick={() => setSidebarOpen(false)}
                />
            )}
        </div>
    )
} 