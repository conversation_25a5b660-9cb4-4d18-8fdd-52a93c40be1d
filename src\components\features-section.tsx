"use client"

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON>,
  Palette,
  Bar<PERSON>hart3,
  Plug,
  MessageSquare,
  Sparkles,
  Zap,
  Shield,
  Clock,
  Globe,
  Users,
  TrendingUp
} from "lucide-react"

const features = [
  {
    icon: Brain,
    title: "AI-Powered Conversations",
    description: "Advanced natural language processing that understands context and provides intelligent responses to your customers.",
    badge: "AI Core",
    color: "text-pink-500",
    bgColor: "bg-pink-500/10",
    features: [
      "GPT-4 powered responses",
      "Context-aware conversations",
      "Multi-language support",
      "Custom training data"
    ]
  },
  {
    icon: Palette,
    title: "Custom Styling & Branding",
    description: "Complete visual customization to match your brand identity with our intuitive design tools.",
    badge: "Design",
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
    features: [
      "Drag & drop builder",
      "Brand color matching",
      "Custom CSS support",
      "Mobile responsive"
    ]
  },
  {
    icon: Bar<PERSON>hart3,
    title: "Advanced Analytics",
    description: "Comprehensive insights into chat performance, user engagement, and conversion metrics.",
    badge: "Analytics",
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
    features: [
      "Real-time dashboard",
      "Conversion tracking",
      "User behavior analysis",
      "Custom reports"
    ]
  },
  {
    icon: Plug,
    title: "Seamless Integrations",
    description: "Connect with your favorite tools and platforms for a unified workflow experience.",
    badge: "Integrations",
    color: "text-green-500",
    bgColor: "bg-green-500/10",
    features: [
      "CRM integrations",
      "Email marketing tools",
      "Webhook support",
      "API access"
    ]
  }
]

const additionalFeatures = [
  {
    icon: MessageSquare,
    title: "Smart Lead Capture",
    description: "Automatically collect and qualify leads through intelligent conversation flows."
  },
  {
    icon: Clock,
    title: "24/7 Availability",
    description: "Provide round-the-clock customer support without increasing your team size."
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Bank-level encryption and compliance with GDPR, CCPA, and SOC 2 standards."
  },
  {
    icon: Globe,
    title: "Global Deployment",
    description: "Deploy across multiple regions with CDN support for optimal performance."
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Multi-user access with role-based permissions and real-time collaboration."
  },
  {
    icon: TrendingUp,
    title: "Performance Optimization",
    description: "AI-driven optimization suggestions to improve chat performance and conversions."
  }
]

export function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-gradient-to-b from-background to-muted/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="glass-card px-4 py-2">
            <Sparkles className="h-4 w-4 mr-2 text-pink-500" />
            Core Features
          </Badge>

          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight">
            Everything you need to{" "}
            <span className="gradient-text">succeed</span>
          </h2>

          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Our comprehensive platform provides all the tools and features you need to create,
            deploy, and optimize AI-powered chat widgets that drive results.
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <Card
              key={feature.title}
              className="glass-card card-professional group cursor-pointer border-white/20"
            >
              <CardHeader className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className={`p-3 rounded-xl ${feature.bgColor}`}>
                    <feature.icon className={`h-6 w-6 ${feature.color}`} />
                  </div>
                  <Badge variant="secondary" className="glass">
                    {feature.badge}
                  </Badge>
                </div>

                <div>
                  <CardTitle className="text-xl group-hover:text-pink-500 transition-colors">
                    {feature.title}
                  </CardTitle>
                  <CardDescription className="text-base mt-2">
                    {feature.description}
                  </CardDescription>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center space-x-2 text-sm">
                      <Zap className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Features */}
        <div className="space-y-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">And much more...</h3>
            <p className="text-muted-foreground">
              Discover additional features that make ChatCraft AI the complete solution for your business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {additionalFeatures.map((feature, index) => (
              <Card
                key={feature.title}
                className="glass-card card-professional group cursor-pointer border-white/20 p-6"
              >
                <div className="flex items-start space-x-4">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-pink-500/10 to-purple-500/10">
                    <feature.icon className="h-5 w-5 text-pink-500" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold group-hover:text-pink-500 transition-colors">
                      {feature.title}
                    </h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
