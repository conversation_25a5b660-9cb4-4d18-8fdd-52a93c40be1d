import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "ChatCraft AI - Build Intelligent Chat Widgets in Minutes",
  description: "Create stunning AI-powered chat widgets for your website with our no-code builder. Boost engagement, capture leads, and provide 24/7 customer support.",
  keywords: ["AI chat widget", "chatbot builder", "customer support", "lead generation", "no-code"],
  authors: [{ name: "ChatCraft AI" }],
  openGraph: {
    title: "ChatCraft AI - Build Intelligent Chat Widgets in Minutes",
    description: "Create stunning AI-powered chat widgets for your website with our no-code builder.",
    type: "website",
    url: "https://chatcraft.ai",
  },
  twitter: {
    card: "summary_large_image",
    title: "ChatCraft AI - Build Intelligent Chat Widgets in Minutes",
    description: "Create stunning AI-powered chat widgets for your website with our no-code builder.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange={false}
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
