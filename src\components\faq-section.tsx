"use client"

import * as React from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  <PERSON>rkles, 
  Plus, 
  Minus, 
  MessageCircle,
  HelpCircle 
} from "lucide-react"

const faqs = [
  {
    question: "How quickly can I set up my first chat widget?",
    answer: "You can have your first AI-powered chat widget up and running in under 5 minutes. Our intuitive drag-and-drop builder makes it incredibly easy to customize the appearance, configure AI responses, and embed the widget on your website with just a few clicks."
  },
  {
    question: "What makes ChatCraft AI different from other chatbot platforms?",
    answer: "ChatCraft AI combines advanced GPT-4 technology with a no-code visual builder, comprehensive analytics, and seamless integrations. Unlike other platforms, we focus on creating natural, context-aware conversations that feel human while providing powerful customization options that don't require technical expertise."
  },
  {
    question: "Can I customize the AI responses for my specific business?",
    answer: "Absolutely! You can train the AI with your own data, create custom conversation flows, set up specific responses for different scenarios, and even define your brand's tone of voice. The AI learns from your business context to provide more accurate and relevant responses to your customers."
  },
  {
    question: "What integrations are available?",
    answer: "We offer 50+ integrations including popular CRM systems (Salesforce, HubSpot), email marketing tools (Mailchimp, Klaviyo), help desk software (Zendesk, Intercom), and e-commerce platforms (Shopify, WooCommerce). We also provide webhooks and a robust API for custom integrations."
  },
  {
    question: "Is there a free trial available?",
    answer: "Yes! We offer a 14-day free trial with full access to all features. No credit card required to start. You can build unlimited chat widgets, test all integrations, and see the full analytics dashboard. After the trial, you can choose the plan that best fits your needs."
  },
  {
    question: "How does the AI handle complex customer inquiries?",
    answer: "Our AI is powered by GPT-4 and trained on millions of customer service interactions. It can understand context, handle multi-turn conversations, and escalate to human agents when needed. You can also set up custom escalation rules and fallback responses for specific scenarios."
  },
  {
    question: "What kind of analytics and reporting do you provide?",
    answer: "Our analytics dashboard provides comprehensive insights including conversation volume, response accuracy, customer satisfaction scores, conversion rates, popular topics, and performance trends. You can export data, create custom reports, and set up automated alerts for important metrics."
  },
  {
    question: "Is my data secure and compliant?",
    answer: "Security is our top priority. We use bank-level encryption, are SOC 2 compliant, and follow GDPR and CCPA regulations. All data is encrypted in transit and at rest, and we provide detailed audit logs. You maintain full control over your data and can delete it at any time."
  }
]

export function FAQSection() {
  const [openItems, setOpenItems] = React.useState<number[]>([0])

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  return (
    <section id="faq" className="py-24 bg-gradient-to-b from-background to-muted/20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="glass-card px-4 py-2">
            <HelpCircle className="h-4 w-4 mr-2 text-pink-500" />
            FAQ
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight">
            Frequently Asked{" "}
            <span className="gradient-text">Questions</span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Everything you need to know about ChatCraft AI. Can't find the answer you're looking for? 
            Feel free to reach out to our support team.
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <Card 
              key={index} 
              className="glass-card border-white/20 overflow-hidden hover-lift"
            >
              <CardContent className="p-0">
                <Button
                  variant="ghost"
                  onClick={() => toggleItem(index)}
                  className="w-full p-6 text-left justify-between hover:bg-white/5 rounded-none h-auto"
                >
                  <span className="font-semibold text-base pr-4">{faq.question}</span>
                  {openItems.includes(index) ? (
                    <Minus className="h-5 w-5 text-pink-500 flex-shrink-0" />
                  ) : (
                    <Plus className="h-5 w-5 text-pink-500 flex-shrink-0" />
                  )}
                </Button>
                
                {openItems.includes(index) && (
                  <div className="px-6 pb-6 animate-slide-up">
                    <div className="pt-2 border-t border-white/10">
                      <p className="text-muted-foreground leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16 space-y-6">
          <div className="glass-card p-8 rounded-2xl">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 rounded-full bg-gradient-to-br from-pink-500/20 to-purple-500/20">
                <MessageCircle className="h-8 w-8 text-pink-500" />
              </div>
            </div>
            
            <h3 className="text-2xl font-bold mb-2">Still have questions?</h3>
            <p className="text-muted-foreground mb-6">
              Our support team is here to help you get the most out of ChatCraft AI.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" className="glass border-white/20">
                Browse Documentation
              </Button>
              <Button className="gradient-pink-purple text-white">
                Contact Support
              </Button>
            </div>
          </div>
          
          <p className="text-sm text-muted-foreground">
            Average response time: Under 2 hours • Available 24/7
          </p>
        </div>
      </div>
    </section>
  )
}
