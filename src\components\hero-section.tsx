"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  ArrowRight,
  Play,
  Star,
  Users,
  MessageCircle,
  Sparkles,
  Zap,
  Shield,
  Bot,
  Send,
  User,
  Settings,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>3
} from "lucide-react"
import { Badge } from "@/components/ui/badge"

export function HeroSection() {
  const [email, setEmail] = React.useState("")
  const [chatMessages, setChatMessages] = React.useState([
    { id: 1, type: 'bot', message: 'Hi! How can I help you today?', time: '2:30 PM' },
    { id: 2, type: 'user', message: 'I need help with pricing', time: '2:31 PM' },
    { id: 3, type: 'bot', message: 'I\'d be happy to help! We have flexible plans starting at $29/month. Would you like to see our pricing options?', time: '2:31 PM' }
  ])
  const [isTyping, setIsTyping] = React.useState(false)
  const [activeTab, setActiveTab] = React.useState('preview')
  const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 })
  const demoRef = React.useRef<HTMLDivElement>(null)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Email submitted:", email)
  }

  const simulateChat = () => {
    setIsTyping(true)
    setTimeout(() => {
      setChatMessages(prev => [...prev, {
        id: prev.length + 1,
        type: 'user',
        message: 'That sounds great! Can I try it for free?',
        time: '2:32 PM'
      }])
      setIsTyping(false)

      setTimeout(() => {
        setIsTyping(true)
        setTimeout(() => {
          setChatMessages(prev => [...prev, {
            id: prev.length + 1,
            type: 'bot',
            message: 'Absolutely! We offer a 14-day free trial with full access to all features. No credit card required!',
            time: '2:32 PM'
          }])
          setIsTyping(false)
        }, 1500)
      }, 1000)
    }, 1000)
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!demoRef.current) return

    const rect = demoRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    const centerX = rect.width / 2
    const centerY = rect.height / 2

    const rotateX = (y - centerY) / centerY * -10
    const rotateY = (x - centerX) / centerX * 10

    setMousePosition({ x: rotateX, y: rotateY })
  }

  const handleMouseLeave = () => {
    setMousePosition({ x: 0, y: 0 })
  }

  return (
    <section className="relative min-h-screen flex items-center overflow-hidden bg-grid">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50/50 via-purple-50/30 to-blue-50/50 dark:from-pink-950/20 dark:via-purple-950/20 dark:to-blue-950/20" />

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 float-3d">
        <div className="glass p-4 rounded-full">
          <MessageCircle className="h-8 w-8 text-pink-500" />
        </div>
      </div>

      <div className="absolute top-40 right-20 float-3d" style={{ animationDelay: "2s" }}>
        <div className="glass p-4 rounded-full">
          <Sparkles className="h-8 w-8 text-purple-500" />
        </div>
      </div>

      <div className="absolute bottom-40 left-20 float-3d" style={{ animationDelay: "4s" }}>
        <div className="glass p-4 rounded-full">
          <Zap className="h-8 w-8 text-blue-500" />
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Left Side - Content */}
          <div className="space-y-8 animate-fade-in">
            {/* Badge */}
            <Badge variant="outline" className="glass-card px-4 py-2 text-sm font-medium w-fit">
              <Sparkles className="h-4 w-4 mr-2 text-pink-500" />
              New: AI-Powered Chat Analytics Dashboard
            </Badge>

            {/* Main Headline */}
            <div className="space-y-6">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight leading-tight">
                Build{" "}
                <span className="gradient-text">Intelligent</span>
                <br />
                Chat Widgets in{" "}
                <span className="gradient-text">Minutes</span>
              </h1>

              <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed max-w-lg">
                Create stunning AI-powered chat widgets for your website with our no-code builder.
                Boost engagement, capture leads, and provide 24/7 customer support.
              </p>
            </div>

            {/* Stats */}
            <div className="flex flex-wrap gap-6 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Star className="h-5 w-5 text-yellow-500 fill-current" />
                <span className="font-semibold">4.9/5</span>
                <span>from 2,000+ reviews</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-green-500" />
                <span className="font-semibold">50,000+</span>
                <span>active users</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-blue-500" />
                <span className="font-semibold">99.9%</span>
                <span>uptime</span>
              </div>
            </div>

            {/* CTA Form */}
            <div className="space-y-4">
              <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-1 glass-card border-white/20 focus:border-pink-500/50 focus:ring-pink-500/20 h-12"
                  required
                />
                <Button
                  type="submit"
                  size="lg"
                  className="gradient-pink-purple text-white hover:opacity-90 transition-opacity group h-12 px-8"
                >
                  Start Free Trial
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </form>
              <p className="text-xs text-muted-foreground">
                No credit card required • 14-day free trial • Cancel anytime
              </p>
            </div>

            {/* Secondary CTA */}
            <div className="flex items-center gap-4">
              <Button variant="outline" size="lg" className="glass-card border-white/20 group">
                <Play className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform" />
                Watch Demo
              </Button>
              <span className="text-sm text-muted-foreground">2 min overview</span>
            </div>
          </div>

          {/* Right Side - Interactive 3D Demo */}
          <div className="relative animate-slide-up">
            <div className="relative perspective-1000">
              {/* Demo Container */}
              <div
                ref={demoRef}
                onMouseMove={handleMouseMove}
                onMouseLeave={handleMouseLeave}
                className="glass-card p-6 rounded-2xl shadow-2xl transition-transform duration-300 ease-out"
                style={{
                  transform: `perspective(1000px) rotateX(${mousePosition.x}deg) rotateY(${mousePosition.y}deg) scale(1.02)`
                }}
              >
                {/* Demo Tabs */}
                <div className="flex space-x-2 mb-6">
                  <button
                    onClick={() => setActiveTab('preview')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${activeTab === 'preview'
                      ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                      : 'glass hover:bg-white/10'
                      }`}
                  >
                    <MessageCircle className="h-4 w-4 inline mr-2" />
                    Live Preview
                  </button>
                  <button
                    onClick={() => setActiveTab('builder')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${activeTab === 'builder'
                      ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                      : 'glass hover:bg-white/10'
                      }`}
                  >
                    <Settings className="h-4 w-4 inline mr-2" />
                    Builder
                  </button>
                  <button
                    onClick={() => setActiveTab('analytics')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${activeTab === 'analytics'
                      ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                      : 'glass hover:bg-white/10'
                      }`}
                  >
                    <BarChart3 className="h-4 w-4 inline mr-2" />
                    Analytics
                  </button>
                </div>

                {/* Demo Content */}
                {activeTab === 'preview' && (
                  <div className="space-y-4">
                    {/* Chat Widget */}
                    <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      {/* Chat Header */}
                      <div className="bg-gradient-to-r from-pink-500 to-purple-600 p-4 text-white">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                            <Bot className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="font-semibold">AI Assistant</h4>
                            <p className="text-xs opacity-90">Online • Typically replies instantly</p>
                          </div>
                        </div>
                      </div>

                      {/* Chat Messages */}
                      <div className="p-4 space-y-3 h-64 overflow-y-auto">
                        {chatMessages.map((msg) => (
                          <div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                            <div className={`max-w-xs px-4 py-2 rounded-2xl ${msg.type === 'user'
                              ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                              : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
                              }`}>
                              <p className="text-sm">{msg.message}</p>
                              <p className={`text-xs mt-1 ${msg.type === 'user' ? 'text-white/70' : 'text-gray-500'
                                }`}>
                                {msg.time}
                              </p>
                            </div>
                          </div>
                        ))}

                        {isTyping && (
                          <div className="flex justify-start">
                            <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 rounded-2xl">
                              <div className="flex space-x-1">
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Chat Input */}
                      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                        <div className="flex space-x-2">
                          <input
                            type="text"
                            placeholder="Type your message..."
                            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-pink-500"
                          />
                          <button
                            onClick={simulateChat}
                            className="p-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-lg hover:opacity-90 transition-opacity"
                          >
                            <Send className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'builder' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="glass p-4 rounded-lg">
                        <Palette className="h-6 w-6 text-pink-500 mb-2" />
                        <h4 className="font-semibold text-sm">Styling</h4>
                        <p className="text-xs text-muted-foreground">Customize colors, fonts, and layout</p>
                      </div>
                      <div className="glass p-4 rounded-lg">
                        <Bot className="h-6 w-6 text-purple-500 mb-2" />
                        <h4 className="font-semibold text-sm">AI Training</h4>
                        <p className="text-xs text-muted-foreground">Train with your business data</p>
                      </div>
                    </div>
                    <div className="glass p-6 rounded-lg text-center">
                      <div className="w-12 h-12 mx-auto bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center mb-3">
                        <Settings className="h-6 w-6 text-white" />
                      </div>
                      <h4 className="font-semibold mb-2">Drag & Drop Builder</h4>
                      <p className="text-sm text-muted-foreground">Build your chat widget visually with our intuitive interface</p>
                    </div>
                  </div>
                )}

                {activeTab === 'analytics' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-3">
                      <div className="glass p-3 rounded-lg text-center">
                        <div className="text-2xl font-bold gradient-text">2.4k</div>
                        <div className="text-xs text-muted-foreground">Conversations</div>
                      </div>
                      <div className="glass p-3 rounded-lg text-center">
                        <div className="text-2xl font-bold gradient-text">94%</div>
                        <div className="text-xs text-muted-foreground">Satisfaction</div>
                      </div>
                      <div className="glass p-3 rounded-lg text-center">
                        <div className="text-2xl font-bold gradient-text">1.2s</div>
                        <div className="text-xs text-muted-foreground">Avg Response</div>
                      </div>
                    </div>
                    <div className="glass p-4 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-sm">Performance Metrics</h4>
                        <BarChart3 className="h-4 w-4 text-pink-500" />
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-xs">
                          <span>Lead Conversion</span>
                          <span className="text-green-500">+23%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div className="bg-gradient-to-r from-pink-500 to-purple-600 h-2 rounded-full w-3/4"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Floating Elements around Demo */}
              <div className="absolute -top-4 -right-4 pulse-3d">
                <div className="glass-card p-3 rounded-full shadow-lg">
                  <Sparkles className="h-5 w-5 text-pink-500" />
                </div>
              </div>

              <div className="absolute -bottom-4 -left-4 float-3d">
                <div className="glass-card p-3 rounded-full shadow-lg">
                  <Zap className="h-5 w-5 text-purple-500" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
