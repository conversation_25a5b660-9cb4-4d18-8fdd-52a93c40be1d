"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  ArrowRight,
  Play,
  Star,
  Users,
  MessageCircle,
  Sparkles,
  Zap,
  Shield
} from "lucide-react"
import { Badge } from "@/components/ui/badge"

export function HeroSection() {
  const [email, setEmail] = React.useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Email submitted:", email)
  }

  return (
    <section className="relative min-h-screen flex items-center overflow-hidden bg-grid">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50/50 via-purple-50/30 to-blue-50/50 dark:from-pink-950/20 dark:via-purple-950/20 dark:to-blue-950/20" />

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 float-elegant">
        <div className="glass p-4 rounded-full">
          <MessageCircle className="h-8 w-8 text-pink-500" />
        </div>
      </div>

      <div className="absolute top-40 right-20 float-elegant" style={{ animationDelay: "2s" }}>
        <div className="glass p-4 rounded-full">
          <Sparkles className="h-8 w-8 text-purple-500" />
        </div>
      </div>

      <div className="absolute bottom-40 left-20 float-elegant" style={{ animationDelay: "4s" }}>
        <div className="glass p-4 rounded-full">
          <Zap className="h-8 w-8 text-blue-500" />
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
        <div className="flex flex-col items-center justify-center min-h-[80vh] text-center">
          {/* Hero Content */}
          <div className="space-y-8 animate-fade-in max-w-4xl">
            {/* Badge */}
            <Badge variant="outline" className="glass-card px-4 py-2 text-sm font-medium mx-auto">
              <Sparkles className="h-4 w-4 mr-2 text-pink-500" />
              New: AI-Powered Chat Analytics Dashboard
            </Badge>

            {/* Main Headline */}
            <div className="space-y-6">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight leading-tight">
                Build{" "}
                <span className="gradient-text">Intelligent</span>
                <br />
                Chat Widgets in{" "}
                <span className="gradient-text">Minutes</span>
              </h1>

              <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
                Create stunning AI-powered chat widgets for your website with our no-code builder.
                Boost engagement, capture leads, and provide 24/7 customer support.
              </p>
            </div>

            {/* Stats */}
            <div className="flex flex-wrap justify-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Star className="h-5 w-5 text-yellow-500 fill-current" />
                <span className="font-semibold">4.9/5</span>
                <span>from 2,000+ reviews</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-green-500" />
                <span className="font-semibold">50,000+</span>
                <span>active users</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-blue-500" />
                <span className="font-semibold">99.9%</span>
                <span>uptime</span>
              </div>
            </div>

            {/* CTA Form */}
            <div className="space-y-4 max-w-md mx-auto">
              <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-1 glass-card border-white/20 focus:border-pink-500/50 focus:ring-pink-500/20 h-12"
                  required
                />
                <Button
                  type="submit"
                  size="lg"
                  className="gradient-pink-purple text-white hover:opacity-90 transition-opacity group h-12 px-8"
                >
                  Start Free Trial
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </form>
              <p className="text-xs text-muted-foreground text-center">
                No credit card required • 14-day free trial • Cancel anytime
              </p>
            </div>

            {/* Secondary CTA */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button variant="outline" size="lg" className="glass-card border-white/20 group">
                <Play className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform" />
                Watch Demo
              </Button>
              <span className="text-sm text-muted-foreground">2 min overview</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
