"use client"

import * as React from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON>, <PERSON>uo<PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"

const testimonials = [
  {
    name: "<PERSON>",
    role: "Head of Marketing",
    company: "TechFlow Inc.",
    avatar: "/api/placeholder/40/40",
    rating: 5,
    content: "ChatCraft AI transformed our customer engagement. We saw a 300% increase in lead generation within the first month. The AI responses are incredibly natural and helpful.",
    metrics: {
      label: "Lead increase",
      value: "300%"
    }
  },
  {
    name: "<PERSON>",
    role: "CEO",
    company: "StartupLab",
    avatar: "/api/placeholder/40/40",
    rating: 5,
    content: "The setup was incredibly easy, and the customization options are endless. Our customers love the instant support, and we've reduced our support ticket volume by 70%.",
    metrics: {
      label: "Ticket reduction",
      value: "70%"
    }
  },
  {
    name: "<PERSON>",
    role: "Customer Success Manager",
    company: "GrowthCorp",
    avatar: "/api/placeholder/40/40",
    rating: 5,
    content: "The analytics dashboard gives us incredible insights into customer behavior. We've optimized our sales funnel and increased conversions by 45% using the data.",
    metrics: {
      label: "Conversion boost",
      value: "45%"
    }
  },
  {
    name: "David Kim",
    role: "Product Manager",
    company: "InnovateTech",
    avatar: "/api/placeholder/40/40",
    rating: 5,
    content: "Integration with our existing tools was seamless. The API is well-documented, and the support team is incredibly responsive. Highly recommend!",
    metrics: {
      label: "Integration time",
      value: "< 1 hour"
    }
  },
  {
    name: "Lisa Thompson",
    role: "E-commerce Director",
    company: "ShopSmart",
    avatar: "/api/placeholder/40/40",
    rating: 5,
    content: "Our online sales have skyrocketed since implementing ChatCraft AI. The chat widget helps customers find exactly what they're looking for instantly.",
    metrics: {
      label: "Sales increase",
      value: "180%"
    }
  },
  {
    name: "Alex Johnson",
    role: "Founder",
    company: "DigitalFirst",
    avatar: "/api/placeholder/40/40",
    rating: 5,
    content: "The multilingual support has been a game-changer for our global expansion. We can now serve customers in 12 languages with consistent quality.",
    metrics: {
      label: "Languages supported",
      value: "12+"
    }
  }
]

const stats = [
  { label: "Customer Satisfaction", value: "98%", icon: "😊" },
  { label: "Average Response Time", value: "< 2s", icon: "⚡" },
  { label: "Uptime Guarantee", value: "99.9%", icon: "🛡️" },
  { label: "Languages Supported", value: "25+", icon: "🌍" }
]

export function TestimonialsSection() {
  return (
    <section id="testimonials" className="py-24 bg-gradient-to-b from-muted/20 to-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="glass-card px-4 py-2">
            <Sparkles className="h-4 w-4 mr-2 text-pink-500" />
            Customer Stories
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight">
            Loved by{" "}
            <span className="gradient-text">thousands</span>
            {" "}of businesses
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            See how companies like yours are using ChatCraft AI to transform their customer experience 
            and drive unprecedented growth.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={stat.label} className="glass-card text-center p-6 hover-lift">
              <div className="text-3xl mb-2">{stat.icon}</div>
              <div className="text-2xl font-bold gradient-text">{stat.value}</div>
              <div className="text-sm text-muted-foreground">{stat.label}</div>
            </Card>
          ))}
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card 
              key={testimonial.name} 
              className="glass-card hover-lift group cursor-pointer border-white/20 relative overflow-hidden"
            >
              <CardContent className="p-6">
                {/* Quote Icon */}
                <Quote className="h-8 w-8 text-pink-500/20 mb-4" />
                
                {/* Rating */}
                <div className="flex items-center space-x-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                  ))}
                </div>
                
                {/* Content */}
                <blockquote className="text-sm leading-relaxed mb-6 text-muted-foreground">
                  "{testimonial.content}"
                </blockquote>
                
                {/* Metrics Badge */}
                <div className="mb-4">
                  <Badge variant="secondary" className="glass text-xs">
                    {testimonial.metrics.label}: {testimonial.metrics.value}
                  </Badge>
                </div>
                
                {/* Author */}
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                    <AvatarFallback className="bg-gradient-to-br from-pink-500 to-purple-600 text-white">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-sm">{testimonial.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {testimonial.role} at {testimonial.company}
                    </div>
                  </div>
                </div>
              </CardContent>
              
              {/* Hover Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-muted-foreground mb-4">
            Join thousands of satisfied customers
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm">
            <div className="flex -space-x-2">
              {[...Array(5)].map((_, i) => (
                <div 
                  key={i}
                  className="w-8 h-8 rounded-full bg-gradient-to-br from-pink-500 to-purple-600 border-2 border-background flex items-center justify-center text-white text-xs font-bold"
                >
                  {String.fromCharCode(65 + i)}
                </div>
              ))}
            </div>
            <span className="text-muted-foreground">and 50,000+ others</span>
          </div>
        </div>
      </div>
    </section>
  )
}
