"use client"

import * as React from "react"
import Link from "next/link"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import {
  MessageCircle,
  Sparkles,
  Sun,
  Moon,
  ArrowLeft,
  Zap,
  Shield,
  Users
} from "lucide-react"

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
  showBackToHome?: boolean
}

export function AuthLayout({ children, title, subtitle, showBackToHome = true }: AuthLayoutProps) {
  const { theme, setTheme } = useTheme()

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50/50 via-purple-50/30 to-blue-50/50 dark:from-pink-950/20 dark:via-purple-950/20 dark:to-blue-950/20 relative overflow-hidden">
      {/* Background Grid */}
      <div className="absolute inset-0 bg-grid opacity-30" />

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 float-elegant">
        <div className="glass p-4 rounded-full">
          <MessageCircle className="h-6 w-6 text-pink-500" />
        </div>
      </div>

      <div className="absolute top-40 right-20 float-elegant" style={{ animationDelay: "2s" }}>
        <div className="glass p-4 rounded-full">
          <Sparkles className="h-6 w-6 text-purple-500" />
        </div>
      </div>

      <div className="absolute bottom-40 left-20 float-elegant" style={{ animationDelay: "4s" }}>
        <div className="glass p-4 rounded-full">
          <Zap className="h-6 w-6 text-blue-500" />
        </div>
      </div>

      {/* Header */}
      <header className="relative z-10 p-6">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="relative">
              <MessageCircle className="h-8 w-8 text-pink-500 group-hover:scale-110 transition-transform duration-300" />
              <Sparkles className="h-4 w-4 text-purple-500 absolute -top-1 -right-1 animate-pulse" />
            </div>
            <span className="text-xl font-bold gradient-text">ChatCraft AI</span>
          </Link>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            {showBackToHome && (
              <Button variant="ghost" asChild className="glass-card border-white/20">
                <Link href="/" className="flex items-center space-x-2">
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to Home</span>
                </Link>
              </Button>
            )}

            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="glass-card border-white/20"
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Branding */}
            <div className="space-y-8 text-center lg:text-left">
              <div className="space-y-4">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight leading-tight">
                  {title}
                </h1>
                <p className="text-xl text-muted-foreground leading-relaxed">
                  {subtitle}
                </p>
              </div>

              {/* Features */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3 justify-center lg:justify-start">
                  <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <Shield className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-sm">Enterprise-grade security</span>
                </div>
                <div className="flex items-center space-x-3 justify-center lg:justify-start">
                  <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <Users className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-sm">Join 50,000+ satisfied users</span>
                </div>
                <div className="flex items-center space-x-3 justify-center lg:justify-start">
                  <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <Zap className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-sm">Setup in under 5 minutes</span>
                </div>
              </div>

              {/* Testimonial */}
              <div className="glass-card p-6 rounded-xl">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                    SC
                  </div>
                  <div>
                    <p className="text-sm italic text-muted-foreground">
                      "ChatCraft AI transformed our customer engagement. Setup was incredibly easy!"
                    </p>
                    <p className="text-xs font-semibold mt-1">Sarah Chen, TechFlow Inc.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Auth Form */}
            <div className="w-full max-w-md mx-auto lg:mx-0">
              <div className="glass-card p-8 rounded-2xl shadow-2xl card-professional">
                {children}
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="fixed bottom-0 left-0 right-0 z-10 p-4">
        <div className="text-center">
          <p className="text-xs text-muted-foreground/60">
            © 2024 ChatCraft AI. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
