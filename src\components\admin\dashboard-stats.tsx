"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
    TrendingUp,
    TrendingDown,
    MessageCircle,
    Users,
    BarChart3,
    Zap,
    DollarSign,
    Clock
} from "lucide-react"

interface StatCardProps {
    title: string
    value: string | number
    change?: {
        value: string
        type: "increase" | "decrease"
    }
    icon: React.ElementType
    description?: string
    color?: "pink" | "purple" | "blue" | "green" | "orange"
}

const colorClasses = {
    pink: {
        icon: "text-pink-500",
        bg: "bg-pink-500/10",
        border: "border-pink-500/20"
    },
    purple: {
        icon: "text-purple-500",
        bg: "bg-purple-500/10",
        border: "border-purple-500/20"
    },
    blue: {
        icon: "text-blue-500",
        bg: "bg-blue-500/10",
        border: "border-blue-500/20"
    },
    green: {
        icon: "text-green-500",
        bg: "bg-green-500/10",
        border: "border-green-500/20"
    },
    orange: {
        icon: "text-orange-500",
        bg: "bg-orange-500/10",
        border: "border-orange-500/20"
    }
}

function StatCard({ title, value, change, icon: Icon, description, color = "pink" }: StatCardProps) {
    const colors = colorClasses[color]

    return (
        <Card className="glass-card border-white/10 hover:border-white/20 transition-all duration-300 card-professional">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                    {title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${colors.bg} ${colors.border} border`}>
                    <Icon className={`h-4 w-4 ${colors.icon}`} />
                </div>
            </CardHeader>
            <CardContent className="space-y-3">
                <div className="flex items-baseline space-x-2">
                    <div className="text-2xl font-bold text-foreground">
                        {value}
                    </div>
                    {change && (
                        <Badge
                            variant="secondary"
                            className={`text-xs ${change.type === "increase"
                                ? "text-green-600 bg-green-500/10 border-green-500/20"
                                : "text-red-600 bg-red-500/10 border-red-500/20"
                                }`}
                        >
                            {change.type === "increase" ? (
                                <TrendingUp className="w-3 h-3 mr-1" />
                            ) : (
                                <TrendingDown className="w-3 h-3 mr-1" />
                            )}
                            {change.value}
                        </Badge>
                    )}
                </div>
                {description && (
                    <p className="text-xs text-muted-foreground leading-relaxed">
                        {description}
                    </p>
                )}
            </CardContent>
        </Card>
    )
}

const defaultStats = [
    {
        title: "Total Conversations",
        value: "12,847",
        change: { value: "+12.5%", type: "increase" as const },
        icon: MessageCircle,
        description: "Active conversations this month",
        color: "pink" as const
    },
    {
        title: "Active Users",
        value: "3,429",
        change: { value: "+8.2%", type: "increase" as const },
        icon: Users,
        description: "Users engaged with chat widgets",
        color: "purple" as const
    },
    {
        title: "Conversion Rate",
        value: "24.8%",
        change: { value: "+3.1%", type: "increase" as const },
        icon: BarChart3,
        description: "Visitors converted to leads",
        color: "blue" as const
    },
    {
        title: "Avg Response Time",
        value: "1.2s",
        change: { value: "-0.3s", type: "increase" as const },
        icon: Zap,
        description: "Average AI response time",
        color: "green" as const
    },
    {
        title: "Revenue Generated",
        value: "$48,392",
        change: { value: "+18.7%", type: "increase" as const },
        icon: DollarSign,
        description: "Revenue attributed to chat widgets",
        color: "orange" as const
    },
    {
        title: "Uptime",
        value: "99.9%",
        change: { value: "+0.1%", type: "increase" as const },
        icon: Clock,
        description: "System availability this month",
        color: "green" as const
    }
]

interface DashboardStatsProps {
    stats?: StatCardProps[]
    className?: string
}

export function DashboardStats({ stats = defaultStats, className }: DashboardStatsProps) {
    return (
        <div className={`grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 ${className}`}>
            {stats.map((stat, index) => (
                <StatCard key={index} {...stat} />
            ))}
        </div>
    )
}

export { StatCard } 