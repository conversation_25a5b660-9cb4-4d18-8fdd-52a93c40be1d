"use client"

import * as React from "react"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
    Plus,
    MessageCircle,
    Bot,
    Palette,
    BarChart3,
    Settings,
    Users,
    Plug,
    ArrowRight,
    Sparkles
} from "lucide-react"

interface QuickActionProps {
    title: string
    description: string
    href: string
    icon: React.ElementType
    color?: "pink" | "purple" | "blue" | "green"
    badge?: string
}

const colorClasses = {
    pink: {
        icon: "text-pink-500",
        bg: "bg-pink-500/10",
        border: "border-pink-500/20",
        hover: "hover:bg-pink-500/20"
    },
    purple: {
        icon: "text-purple-500",
        bg: "bg-purple-500/10",
        border: "border-purple-500/20",
        hover: "hover:bg-purple-500/20"
    },
    blue: {
        icon: "text-blue-500",
        bg: "bg-blue-500/10",
        border: "border-blue-500/20",
        hover: "hover:bg-blue-500/20"
    },
    green: {
        icon: "text-green-500",
        bg: "bg-green-500/10",
        border: "border-green-500/20",
        hover: "hover:bg-green-500/20"
    }
}

function QuickActionCard({ title, description, href, icon: Icon, color = "pink", badge }: QuickActionProps) {
    const colors = colorClasses[color]

    return (
        <Link href={href}>
            <Card className={`glass-card border-white/10 hover:border-white/20 transition-all duration-300 card-professional group cursor-pointer ${colors.hover}`}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <div className={`p-3 rounded-xl ${colors.bg} ${colors.border} border group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className={`h-5 w-5 ${colors.icon}`} />
                    </div>
                    {badge && (
                        <Badge variant="secondary" className="text-xs">
                            {badge}
                        </Badge>
                    )}
                </CardHeader>
                <CardContent className="space-y-3">
                    <div className="space-y-2">
                        <h3 className="font-semibold text-foreground group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors">
                            {title}
                        </h3>
                        <p className="text-sm text-muted-foreground leading-relaxed">
                            {description}
                        </p>
                    </div>
                    <div className="flex items-center text-sm text-pink-600 dark:text-pink-400 group-hover:translate-x-1 transition-transform duration-300">
                        <span className="mr-1">Get started</span>
                        <ArrowRight className="h-3 w-3" />
                    </div>
                </CardContent>
            </Card>
        </Link>
    )
}

const defaultQuickActions = [
    {
        title: "Create New Widget",
        description: "Build a new AI-powered chat widget for your website with our visual builder.",
        href: "/admin/widgets/new",
        icon: Plus,
        color: "pink" as const,
        badge: "Popular"
    },
    {
        title: "Manage Chat Widgets",
        description: "View, edit, and configure your existing chat widgets and their settings.",
        href: "/admin/widgets",
        icon: MessageCircle,
        color: "purple" as const
    },
    {
        title: "Configure AI Models",
        description: "Set up and manage AI providers, models, and training data for better responses.",
        href: "/admin/ai-models",
        icon: Bot,
        color: "blue" as const
    },
    {
        title: "Customize Appearance",
        description: "Personalize your chat widgets with custom colors, fonts, and branding.",
        href: "/admin/customization",
        icon: Palette,
        color: "green" as const
    },
    {
        title: "View Analytics",
        description: "Track performance metrics, user engagement, and conversion rates.",
        href: "/admin/analytics",
        icon: BarChart3,
        color: "pink" as const
    },
    {
        title: "Manage Users",
        description: "Add team members, set permissions, and manage user access levels.",
        href: "/admin/users",
        icon: Users,
        color: "purple" as const
    }
]

interface RecentActivityItem {
    id: string
    type: "widget_created" | "user_added" | "analytics_milestone" | "integration_connected"
    title: string
    description: string
    timestamp: string
    icon: React.ElementType
    color: "pink" | "purple" | "blue" | "green"
}

const recentActivities: RecentActivityItem[] = [
    {
        id: "1",
        type: "widget_created",
        title: "New widget created",
        description: "Support Chat Widget v2.1 was successfully deployed",
        timestamp: "2 hours ago",
        icon: MessageCircle,
        color: "pink"
    },
    {
        id: "2",
        type: "analytics_milestone",
        title: "Milestone reached",
        description: "10,000 conversations completed this month",
        timestamp: "5 hours ago",
        icon: BarChart3,
        color: "blue"
    },
    {
        id: "3",
        type: "integration_connected",
        title: "Integration connected",
        description: "Slack workspace successfully linked",
        timestamp: "1 day ago",
        icon: Plug,
        color: "green"
    },
    {
        id: "4",
        type: "user_added",
        title: "New team member",
        description: "Sarah Johnson joined as Content Manager",
        timestamp: "2 days ago",
        icon: Users,
        color: "purple"
    }
]

interface DashboardWidgetsProps {
    quickActions?: QuickActionProps[]
    showRecentActivity?: boolean
    className?: string
}

export function DashboardWidgets({
    quickActions = defaultQuickActions,
    showRecentActivity = true,
    className
}: DashboardWidgetsProps) {
    return (
        <div className={`space-y-6 ${className}`}>
            {/* Quick Actions */}
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-foreground">Quick Actions</h2>
                    <Button variant="ghost" size="sm" asChild>
                        <Link href="/admin/widgets">
                            View all
                            <ArrowRight className="ml-1 h-3 w-3" />
                        </Link>
                    </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    {quickActions.map((action, index) => (
                        <QuickActionCard key={index} {...action} />
                    ))}
                </div>
            </div>

            {/* Recent Activity */}
            {showRecentActivity && (
                <div className="space-y-4">
                    <h2 className="text-xl font-semibold text-foreground">Recent Activity</h2>
                    <Card className="glass-card border-white/10">
                        <CardContent className="p-6">
                            <div className="space-y-4">
                                {recentActivities.map((activity) => {
                                    const Icon = activity.icon
                                    const colors = colorClasses[activity.color]

                                    return (
                                        <div key={activity.id} className="flex items-start space-x-4 p-3 rounded-lg hover:bg-white/5 transition-colors">
                                            <div className={`p-2 rounded-lg ${colors.bg} ${colors.border} border flex-shrink-0`}>
                                                <Icon className={`h-4 w-4 ${colors.icon}`} />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <h4 className="text-sm font-medium text-foreground">
                                                        {activity.title}
                                                    </h4>
                                                    <span className="text-xs text-muted-foreground">
                                                        {activity.timestamp}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-muted-foreground mt-1">
                                                    {activity.description}
                                                </p>
                                            </div>
                                        </div>
                                    )
                                })}
                            </div>
                            <div className="mt-4 pt-4 border-t border-white/10">
                                <Button variant="ghost" size="sm" className="w-full">
                                    View all activity
                                    <ArrowRight className="ml-1 h-3 w-3" />
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
    )
}

export { QuickActionCard } 