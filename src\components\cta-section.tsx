"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowRight, 
  Sparkles, 
  Zap, 
  Shield, 
  Clock,
  CheckCircle,
  Star
} from "lucide-react"

const benefits = [
  "14-day free trial",
  "No credit card required", 
  "Cancel anytime",
  "24/7 support"
]

const features = [
  {
    icon: Zap,
    text: "Setup in 5 minutes"
  },
  {
    icon: Shield,
    text: "Enterprise security"
  },
  {
    icon: Clock,
    text: "24/7 availability"
  }
]

export function CTASection() {
  const [email, setEmail] = React.useState("")
  const [isSubmitted, setIsSubmitted] = React.useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle email submission
    console.log("Email submitted:", email)
    setIsSubmitted(true)
    
    // Reset after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false)
      setEmail("")
    }, 3000)
  }

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 dark:from-pink-950/20 dark:via-purple-950/20 dark:to-blue-950/20" />
      <div className="absolute inset-0 bg-grid opacity-30" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 animate-float">
        <div className="glass p-4 rounded-full">
          <Sparkles className="h-6 w-6 text-pink-500" />
        </div>
      </div>
      
      <div className="absolute bottom-20 right-10 animate-float" style={{ animationDelay: "3s" }}>
        <div className="glass p-4 rounded-full">
          <Zap className="h-6 w-6 text-purple-500" />
        </div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Badge */}
        <Badge variant="outline" className="glass-card px-4 py-2 mb-8">
          <Star className="h-4 w-4 mr-2 text-yellow-500 fill-current" />
          Join 50,000+ satisfied customers
        </Badge>

        {/* Main Content */}
        <div className="space-y-8">
          <div className="space-y-4">
            <h2 className="text-3xl sm:text-4xl lg:text-6xl font-bold tracking-tight">
              Ready to transform your{" "}
              <span className="gradient-text">customer experience?</span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Start building intelligent chat widgets today and see the difference AI can make 
              for your business. No technical skills required.
            </p>
          </div>

          {/* Features */}
          <div className="flex flex-wrap justify-center gap-6 text-sm">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center space-x-2 glass-card px-4 py-2 rounded-full">
                <feature.icon className="h-4 w-4 text-pink-500" />
                <span>{feature.text}</span>
              </div>
            ))}
          </div>

          {/* Email Form */}
          <div className="max-w-md mx-auto">
            {!isSubmitted ? (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <Input
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="flex-1 glass-card border-white/20 focus:border-pink-500/50 focus:ring-pink-500/20 h-12"
                    required
                  />
                  <Button 
                    type="submit"
                    size="lg"
                    className="gradient-pink-purple text-white hover:opacity-90 transition-opacity group h-12 px-8"
                  >
                    Start Free Trial
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
                
                {/* Benefits */}
                <div className="flex flex-wrap justify-center gap-4 text-xs text-muted-foreground">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center space-x-1">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span>{benefit}</span>
                    </div>
                  ))}
                </div>
              </form>
            ) : (
              <div className="glass-card p-8 rounded-2xl animate-fade-in">
                <div className="flex items-center justify-center mb-4">
                  <div className="p-3 rounded-full bg-green-500/20">
                    <CheckCircle className="h-8 w-8 text-green-500" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold mb-2">Welcome aboard! 🎉</h3>
                <p className="text-muted-foreground">
                  Check your email for setup instructions and your free trial access.
                </p>
              </div>
            )}
          </div>

          {/* Secondary CTA */}
          <div className="pt-8 border-t border-white/10">
            <p className="text-sm text-muted-foreground mb-4">
              Want to see it in action first?
            </p>
            <Button variant="outline" className="glass-card border-white/20">
              Watch 2-minute Demo
            </Button>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 pt-8 border-t border-white/10">
          <p className="text-sm text-muted-foreground mb-6">
            Trusted by leading companies worldwide
          </p>
          
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {/* Company logos would go here */}
            <div className="glass-card px-6 py-3 rounded-lg">
              <span className="font-semibold text-sm">TechFlow</span>
            </div>
            <div className="glass-card px-6 py-3 rounded-lg">
              <span className="font-semibold text-sm">StartupLab</span>
            </div>
            <div className="glass-card px-6 py-3 rounded-lg">
              <span className="font-semibold text-sm">GrowthCorp</span>
            </div>
            <div className="glass-card px-6 py-3 rounded-lg">
              <span className="font-semibold text-sm">InnovateTech</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
